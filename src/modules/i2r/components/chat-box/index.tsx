import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { Divider } from '@heroui/react';

import Textarea from '@/components/textarea';
import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import FileUploadButton from '@/components/file-upload-button';
import TicketSelectionModal from '@/modules/platform/components/ticket-selection-modal';
import OptionsMenu from '../options-menu';
import SkeletonLoader from '@/components/skeleton-loader';

import {
  PaperAirplaneIcon,
  ArrowUpTrayIcon,
  SparklesIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

import axiosInstance from '@/utils/axios';
import { useI2RPollingContext } from '../../contexts/polling.context';
import { SubModules, Modules } from '@/modules/platform/interfaces/modules';
import { IJiraTicket } from '@/modules/platform/interfaces/tickets';

export interface ChatMessage {
  id: string;
  sender: 'user' | 'ai' | 'system';
  text: string;
  timestamp?: string;
  showActions?: boolean;
  subType?: string;
  isLoading?: boolean;
  file?: File;
}

export interface ChatBoxProps {
  messages: ChatMessage[];
  onSend: (message: string) => void;
  isLoading?: boolean;
  placeholder?: string;
  onLike?: (id: string) => void;
  onDislike?: (id: string) => void;
  onRegenerate?: (id: string) => void;
  onOpenRightPanel?: () => void;
  chatId: string;
  onFileUpload?: (file: File) => void;
  isFileUploading?: boolean;
}

const UserMessage: React.FC<{ message: ChatMessage }> = ({ message }) => (
  <div className="flex justify-end">
    <div className="bg-secondary-neutral-200 text-neutral-900 rounded-xl px-4 py-3 max-w-md">
      {message.subType === 'file' && message.file ? (
        <div className="space-y-2">
          <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">{message.text}</p>
          <div className="flex items-center gap-2 p-2 bg-white/50 rounded border">
            <div className="flex-shrink-0">
              {message.file.type.startsWith('image/') ? (
                <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                  <span className="text-blue-600 text-xs">IMG</span>
                </div>
              ) : message.file.type.includes('pdf') ? (
                <div className="w-8 h-8 bg-red-100 rounded flex items-center justify-center">
                  <span className="text-red-600 text-xs font-bold">PDF</span>
                </div>
              ) : message.file.type.includes('word') ? (
                <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                  <span className="text-blue-600 text-xs font-bold">DOC</span>
                </div>
              ) : message.file.type.includes('text') ? (
                <div className="w-8 h-8 bg-green-100 rounded flex items-center justify-center">
                  <span className="text-green-600 text-xs font-bold">TXT</span>
                </div>
              ) : message.file.type.includes('csv') ? (
                <div className="w-8 h-8 bg-purple-100 rounded flex items-center justify-center">
                  <span className="text-purple-600 text-xs font-bold">CSV</span>
                </div>
              ) : (
                <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                  <span className="text-gray-600 text-xs">FILE</span>
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium text-gray-900 truncate">
                {message.file.name}
              </p>
              <p className="text-xs text-gray-500">
                {(message.file.size / 1024).toFixed(1)} KB
              </p>
            </div>
          </div>
        </div>
      ) : (
        <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">{message.text}</p>
      )}
    </div>
  </div>
);

const AIMessage: React.FC<{
  message: ChatMessage;
  prdData: any;
  setPrdData: any;
  onOpenRightPanel?: () => void;
}> = ({ message, prdData, setPrdData, onOpenRightPanel }) => (
  <div className="flex justify-start">
    <div className="max-w-4xl w-full bg-white rounded-xl p-4 border">
      {message.showActions && (
        <div className="flex items-center gap-2 mb-2">
          <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
            <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
          </div>
        </div>
      )}

      {message.isLoading ? (
        <SkeletonLoader lines={3} className="mt-2" />
      ) : (
        <p className="text-neutral-900 text-sm leading-relaxed whitespace-pre-wrap break-words">
          {message.text}
        </p>
      )}

      {message.showActions && (
        <>
          <Divider className="my-4" />
          <div className="flex gap-2 items-center">
            <OptionsMenu
              isLikeEnabled={prdData?.liked === null || !prdData?.liked}
              isDislikeEnabled={prdData?.liked === null || prdData?.liked}
              isRegenerateEnabled={true}
              isEditEnabled={false}
              showPublish={false}
              openRegenerationModal={() => { }}
              setRegenerationConfig={() => { }}
              type={SubModules.PRD}
              id={prdData?.id || ''}
              showOpen={true}
              onOpen={onOpenRightPanel}
              setPrdData={setPrdData}
              showEditButton={false}
            />
          </div>
        </>
      )}
    </div>
  </div>
);

const SuggestionSection: React.FC = () => (
  <div>
    <div className="flex items-center gap-2 mb-4">
      <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
        <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
      </div>
      <span className="label-m text-secondary-neutral-900">
        Hey there! Here are some next steps to consider:
      </span>
    </div>

    <div className="flex gap-2">
      <Button
        variant={ButtonVariant.FLAT}
        className="flex w-fit items-center gap-2 rounded-xl border px-3"
      >
        <div className="label-xs text-secondary-neutral-600">
          Generate Epics & User Stories
        </div>
      </Button>
      <Button
        variant={ButtonVariant.FLAT}
        className="flex w-fit items-center gap-2 rounded-xl border px-3"
      >
        <div className="label-xs text-secondary-neutral-600">
          Regenerate PRD
        </div>
      </Button>
    </div>
  </div>
);

const ChatInput: React.FC<{
  control: any;
  onJiraIconClick: () => void;
  onFileUpload: (file: File) => void;
  placeholder: string;
  errorMessage: string;
  handleKeyUp: (e: React.KeyboardEvent) => void;
  errors: any;
  handleSubmit: any;
  handleSendMessage: (data: { message: string }) => void;
  disableSubmit?: boolean;
  uploadedFile?: File | null;
  setUploadedFile: (file: File | null) => void;
  messageValue?: string;
}> = ({
  control,
  onJiraIconClick,
  onFileUpload,
  placeholder,
  errorMessage,
  handleKeyUp,
  errors,
  handleSubmit,
  handleSendMessage,
  disableSubmit,
  uploadedFile,
  setUploadedFile,
  messageValue,
}) => {
    return (
      <Controller
        name="message"
        control={control}
        rules={{ required: errorMessage }}
        render={({ field }) => (
          <div className="w-full">
            <div className="w-full bg-white rounded-xl border border-gray-200 p-4 pt-3">
              {uploadedFile && uploadedFile.type.startsWith('image/') && (
                <div className="relative w-32 h-24 rounded-md overflow-hidden border border-gray-200 mb-3">
                  <Image
                    src={URL.createObjectURL(uploadedFile)}
                    alt={uploadedFile.name}
                    fill
                    className="object-cover"
                  />
                  <button
                    className="absolute top-1 right-1 bg-white/80 rounded-full p-1 hover:bg-white"
                    type="button"
                    onClick={() => setUploadedFile(null)}
                  >
                    <XMarkIcon className="w-4 h-4 text-gray-500" />
                  </button>
                </div>
              )}

              <Textarea
                {...field}
                className="text-sm leading-tight p-0 min-h-[24px] resize-none focus:ring-0 focus:outline-none"
                placeholder={placeholder}
                isInvalid={!!errors.message}
                errorMessage={errors.message?.message}
                isRequired
                classNames={{
                  inputWrapper: 'min-h-[24px] p-0 bg-transparent border-none shadow-none',
                  innerWrapper: 'min-h-[24px] items-center p-0',
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                  }
                }}
                onKeyUp={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleKeyUp(e);
                  }
                }}
              />

              <div className="flex items-center gap-2 mt-2">
                <Image
                  className="cursor-pointer"
                  src="/icons/jira.svg"
                  alt="jira logo"
                  width={20}
                  height={20}
                  onClick={onJiraIconClick}
                />
                <div className="w-px h-6 bg-secondary-neutral-300" />
                <FileUploadButton
                  icon={<ArrowUpTrayIcon className="h-6 w-6 text-secondary-neutral-400" />}
                  onFileSelect={onFileUpload}
                />
                <div className="flex-1" />
                <PaperAirplaneIcon
                  className={`h-6 w-6 cursor-pointer ${disableSubmit || (!messageValue?.trim() && !uploadedFile)
                    ? 'text-secondary-neutral-300'
                    : 'text-secondary-neutral-600'
                    }`}
                  onClick={
                    disableSubmit || (!messageValue?.trim() && !uploadedFile)
                      ? undefined
                      : () => {
                        if (uploadedFile && !messageValue?.trim()) {
                          setUploadedFile(null);
                        } else {
                          handleSubmit(handleSendMessage)();
                        }
                      }
                  }
                />
              </div>
            </div>
          </div>
        )}
      />
    );
  };

const ChatBox: React.FC<ChatBoxProps> = ({
  messages,
  onSend,
  onOpenRightPanel,
  chatId,
  onFileUpload,
  isFileUploading
}) => {
  const { prdData, setPrdData } = useI2RPollingContext();
  const homepageConstants = useTranslations('I2R.homepage');

  const { control, handleSubmit, reset, watch, formState: { errors } } = useForm({
    defaultValues: { message: '' }
  });
  const messageValue = watch('message');

  const messagesEndRef = useRef<HTMLDivElement>(null);

  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<'epics' | 'user-stories'>('epics');
  const [allAIReadyEpics, setAllAIReadyEpics] = useState<IJiraTicket[]>([]);
  const [allAIReadyStories, setAllAIReadyStories] = useState<IJiraTicket[]>([]);
  const [selectedEpics, setSelectedEpics] = useState<IJiraTicket[]>([]);
  const [selectedStories, setSelectedStories] = useState<IJiraTicket[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const handleSendMessage = useCallback((data: { message: string }) => {
    if (disableSubmit) {
      return;
    }

    if (data.message && data.message.trim()) {
      setDisableSubmit(true);

      try {
        onSend(data.message.trim());
        reset();
        setUploadedFile(null); // Clear the uploaded file after sending
      } catch (error) {
        console.warn('Error sending message:', error);
      } finally {
        setDisableSubmit(false);
      }
    }
    // If validation fails, form won't submit and error will show
  }, [onSend, reset, disableSubmit, setUploadedFile]);

  const handleKeyUp = (e: React.KeyboardEvent) => {
    if (disableSubmit) {
      return;
    }

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(handleSendMessage)();
    }
  };

  const fetchTickets = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await axiosInstance.get('/api/platform/jira');
      const data = response.data || [];
      setAllAIReadyEpics(data.filter((t: IJiraTicket) => t.type.toLowerCase() === 'epic'));
      setAllAIReadyStories(data.filter((t: IJiraTicket) => t.type.toLowerCase() === 'user-story'));
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleJiraIconClick = useCallback(() => {
    setIsTicketModalOpen(true);
    if (!allAIReadyEpics.length && !allAIReadyStories.length) {
      fetchTickets();
    }
  }, [allAIReadyEpics.length, allAIReadyStories.length, fetchTickets]);

  const handleFileUpload = useCallback((file: File) => {
    console.log('Selected file:', file);

    // Validate file type
    const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    const allowedDocumentTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
      'application/pdf',
      'text/plain', // TXT
      'text/csv' // CSV
    ];

    const isValidType = [...allowedImageTypes, ...allowedDocumentTypes].includes(file.type);

    if (!isValidType) {
      alert('Only JPEG, JPG, PNG, DOCX, PDF, TXT, or CSV files are allowed.');
      return;
    }

    setUploadedFile(file);

    if (onFileUpload) {
      onFileUpload(file);
    }
  }, [onFileUpload]);

  const fetchPrd = useCallback(async (chatId: string) => {
    try {
      const response = await axiosInstance.get('/api/platform/requirement-document', {
        params: { chatId, documentType: SubModules.PRD },
      });
      setPrdData(response.data);
      return response.data;
    } catch (error) {
      return null;
    }
  }, [setPrdData]);

  useEffect(() => {
    if (messagesEndRef.current && typeof messagesEndRef.current.scrollIntoView === 'function') {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  useEffect(() => {
    fetchPrd(chatId);
  }, [chatId, fetchPrd]);

  const renderMessage = useCallback((message: ChatMessage) => {
    if (message.sender === 'user') {
      return <UserMessage message={message} />;
    }

    return (
      <AIMessage
        message={message}
        prdData={prdData}
        setPrdData={setPrdData}
        onOpenRightPanel={onOpenRightPanel}
      />
    );
  }, [prdData, setPrdData, onOpenRightPanel]);

  return (
    <div className="flex flex-col h-full w-full space-y-4">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto space-y-6">
        {messages.map((message) => (
          <div key={message.id}>
            {renderMessage(message)}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <SuggestionSection />

      <ChatInput
        control={control}
        onJiraIconClick={handleJiraIconClick}
        onFileUpload={handleFileUpload}
        placeholder={homepageConstants('inputs.idea.placeholder')}
        errorMessage={homepageConstants('inputs.idea.error')}
        handleKeyUp={handleKeyUp}
        errors={errors}
        handleSubmit={handleSubmit}
        handleSendMessage={handleSendMessage}
        disableSubmit={disableSubmit}
        uploadedFile={uploadedFile}
        setUploadedFile={setUploadedFile}
        messageValue={messageValue}
      />

      {/* Ticket Selection Modal */}
      <TicketSelectionModal
        title="Select Jira Tickets"
        noTicketsError="No tickets found"
        maxLimitError="You can select up to 5 tickets"
        isOpen={isTicketModalOpen}
        onClose={() => setIsTicketModalOpen(false)}
        selectedOption={selectedOption}
        setSelectedOption={setSelectedOption}
        allAIReadyEpics={allAIReadyEpics}
        allAIReadyStories={allAIReadyStories}
        selectedEpics={selectedEpics}
        selectedStories={selectedStories}
        setSelectedEpics={setSelectedEpics}
        setSelectedStories={setSelectedStories}
        isLoading={isLoading}
      />
    </div>
  );
};

export default ChatBox; 